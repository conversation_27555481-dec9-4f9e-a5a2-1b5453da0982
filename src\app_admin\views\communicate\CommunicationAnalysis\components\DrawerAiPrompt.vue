<template>
  <el-drawer
    v-model="is_show"
    direction="rtl"
    class="drawer_ai_prompt"
    :close-on-click-modal="true"
  >
    <template #header>
      <div class="vd_title">提示词Prompt</div>
    </template>
    <template #default>
      <ChatAiBox ref="refChatAiBox" />
    </template>
  </el-drawer>
</template>

<script setup>
import ChatAiBox from "@/components/chatAiBox/ChatAiBox.vue";
const refChatAiBox = ref();
const is_show = ref(false);
const emit = defineEmits(["callback"]);

const props = defineProps({
  readonly: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const onCancel = () => {
  is_show.value = false;
};

const onConfirm = () => {
  refVisitDetail.value.onConfirm();
};

const onCallback = (type, data) => {
  is_show.value = false;
  if (type == "reload") {
    emit("callback", "reload");
  } else if (type == "edit_plan") {
    emit("callback", "edit_plan", data);
  } else if (type == "has_error") {
    is_show.value = false;
  }
};

const show = () => {
  is_show.value = true;
  nextTick(() => {
    const appid = "gen_prompt_aibox";
    const config = toRaw(g.clientStore.getAppConfig(appid));
    refChatAiBox.value.init(config);
  });
};

defineExpose({
  show,
  onCancel,
  onConfirm,
  is_show,
  ChatAiBox,
  refChatAiBox,
});
</script>

<style lang="scss">
.drawer_ai_prompt {
  width: 600px !important;

  .el-drawer__header {
    height: 56px;
    padding: 0;
    border: 1px solid #e9e9e9;
    font-size: 16px;
    color: #262626;
    margin-bottom: 0;
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0;
    margin: 0;

    .ed_main {
      height: calc(100vh - 100px);

      .av_item {
        .av_item_value {
          width: 90%;
        }
      }
    }

    .msg_ai {
      .mbody {
        background: #bdd2ff;
      }
    }

    .msg_my {
      .mbody {
        background: #fff;
      }
    }
  }
}
</style>
